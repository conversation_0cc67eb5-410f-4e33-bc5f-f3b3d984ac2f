/* Custom styles for MessagePlayer component */

/* Volume slider styling */
.slider {
  background: linear-gradient(to right, #f472b6 0%, #a855f7 100%);
  outline: none;
  border-radius: 15px;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(45deg, #f472b6, #a855f7);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: linear-gradient(45deg, #f472b6, #a855f7);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

/* Custom animations */
@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateX(-10px); 
  }
  to { 
    opacity: 1; 
    transform: translateX(0); 
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

@keyframes heartFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    filter: hue-rotate(0deg);
  }
  25% {
    transform: translateY(-15px) rotate(10deg) scale(1.1);
    filter: hue-rotate(90deg);
  }
  50% {
    transform: translateY(-8px) rotate(-8deg) scale(0.9);
    filter: hue-rotate(180deg);
  }
  75% {
    transform: translateY(-20px) rotate(5deg) scale(1.2);
    filter: hue-rotate(270deg);
  }
}

.heart-float {
  animation: heartFloat 3s ease-in-out infinite;
}

/* Magical sparkle animation */
@keyframes sparkleRotate {
  0% { transform: rotate(0deg) scale(1); opacity: 1; }
  50% { transform: rotate(180deg) scale(1.5); opacity: 0.7; }
  100% { transform: rotate(360deg) scale(1); opacity: 1; }
}

.sparkle-rotate {
  animation: sparkleRotate 2s linear infinite;
}

/* Hover effects */
.hover\:scale-102:hover {
  transform: scale(1.02);
}

/* Playing indicator animation */
@keyframes equalizer {
  0%, 100% { height: 4px; }
  50% { height: 16px; }
}

.equalizer-bar {
  animation: equalizer 1s ease-in-out infinite;
}

.equalizer-bar:nth-child(2) {
  animation-delay: 0.2s;
}

.equalizer-bar:nth-child(3) {
  animation-delay: 0.4s;
}

/* Glow effect for active elements */
.glow-pink {
  box-shadow: 0 0 20px rgba(244, 114, 182, 0.3);
}

.glow-purple {
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
}

/* Enhanced progress bar animation */
@keyframes progressPulse {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 20px rgba(236, 72, 153, 0.4);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 30px rgba(236, 72, 153, 0.8);
  }
}

.progress-pulse {
  animation: progressPulse 1.5s ease-in-out infinite;
}

/* Cute bounce animation */
@keyframes cuteBounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
  40% { transform: translateY(-10px) scale(1.1); }
  60% { transform: translateY(-5px) scale(1.05); }
}

.cute-bounce {
  animation: cuteBounce 2s ease-in-out infinite;
}

/* Rainbow text animation */
@keyframes rainbow {
  0% { filter: hue-rotate(0deg); }
  100% { filter: hue-rotate(360deg); }
}

.rainbow-text {
  animation: rainbow 3s linear infinite;
}

/* Floating animation */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(2deg); }
  66% { transform: translateY(-5px) rotate(-2deg); }
}

.float-animation {
  animation: float 4s ease-in-out infinite;
}

/* Magical glow pulse */
@keyframes magicalGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(236, 72, 153, 0.3),
                0 0 40px rgba(168, 85, 247, 0.2),
                0 0 60px rgba(6, 182, 212, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(236, 72, 153, 0.6),
                0 0 60px rgba(168, 85, 247, 0.4),
                0 0 90px rgba(6, 182, 212, 0.3);
  }
}

.magical-glow {
  animation: magicalGlow 2s ease-in-out infinite;
}
