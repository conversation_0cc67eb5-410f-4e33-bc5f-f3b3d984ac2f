@tailwind base;
@tailwind components;
@tailwind utilities;

/* Emoji Font Support - Ensure proper emoji rendering across all platforms */
@layer base {
  * {
    font-family:
      /* System fonts first */
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
      /* Emoji fonts for cross-platform support */
      'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', 'Android Emoji', 'EmojiSymbols',
      /* Fallback fonts */
      sans-serif, 'Helvetica Neue', Arial;
  }

  /* Specific emoji class for better control */
  .emoji {
    font-family:
      'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', 'Android Emoji', 'EmojiSymbols',
      'Symbola', 'DejaVu Sans', sans-serif !important;
    font-variant-emoji: emoji;
    font-feature-settings: "liga" off;
    font-style: normal !important;
    font-weight: normal !important;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Thai font support with emoji fallback */
  .font-thai {
    font-family:
      'Sarabun', 'Prompt', 'Kanit', 'Noto Sans Thai', 'Leelawadee UI',
      /* Emoji support */
      'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', 'Android Emoji',
      /* System fallbacks */
      -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }
}

/* Enhanced smooth scrolling and mobile optimizations */
html {
  scroll-behavior: smooth;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  button:hover {
    transform: none;
  }

  button:active {
    transform: scale(0.95);
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #ec4899, #a855f7);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #db2777, #9333ea);
}

/* Prevent scroll-to-top in jigsaw puzzle - Only for pieces */
.jigsaw-piece {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  cursor: grab;
  text-decoration: none;
}

.jigsaw-piece:active {
  cursor: grabbing;
}

/* Prevent focus scrolling only on pieces */
.jigsaw-piece:focus {
  outline: none;
}

/* Mobile performance optimizations */
.jigsaw-piece {
  transform: translateZ(0); /* Force hardware acceleration */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  perspective: 1000px;
  -webkit-perspective: 1000px;
  touch-action: none; /* Prevent scrolling during touch */
}

/* Prevent scrolling on puzzle container during drag */
.puzzle-container {
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Optimize transitions for mobile */
@media (max-width: 768px) {
  .jigsaw-piece {
    transition: none !important; /* Disable all transitions on mobile for better performance */
    transform: translateZ(0) !important; /* Force hardware acceleration */
    -webkit-transform: translateZ(0) !important;
  }

  .jigsaw-piece:hover {
    transform: translateZ(0) !important; /* Disable hover effects on mobile */
    -webkit-transform: translateZ(0) !important;
  }

  .jigsaw-piece:active {
    transform: translateZ(0) scale(1.02) !important; /* Minimal scale for feedback */
    -webkit-transform: translateZ(0) scale(1.02) !important;
  }

  /* Optimize puzzle container for mobile */
  .puzzle-container {
    -webkit-overflow-scrolling: touch;
    overflow: hidden;
    position: relative;
  }

  /* Reduce visual complexity on mobile */
  .jigsaw-piece .shadow-lg {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  }
}

/* Completion animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.6s ease-out;
}

.animate-fadeInScale {
  animation: fadeInScale 0.8s ease-out;
}

.animate-slideInUp {
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

/* Magical reveal animations */
@keyframes twinkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0.3) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

@keyframes magicalGrow {
  0% {
    opacity: 0;
    transform: scale(0.3) rotate(-5deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05) rotate(2deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes gentleFadeIn {
  0% {
    opacity: 0;
    filter: blur(10px);
  }
  100% {
    opacity: 1;
    filter: blur(0px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(5deg);
  }
  66% {
    transform: translateY(-5px) rotate(-3deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

.animate-twinkle {
  animation: twinkle 3s ease-in-out infinite;
}

.animate-sparkle {
  animation: sparkle 2s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin 3s linear infinite;
}

.animate-magicalGrow {
  animation: magicalGrow 2s ease-out;
}

.animate-gentleFadeIn {
  animation: gentleFadeIn 2.5s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-shimmer {
  background-size: 200% auto;
  animation: shimmer 3s linear infinite;
}

/* Global scroll lock during jigsaw play */
body.jigsaw-playing {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
  touch-action: none !important;
}

/* Prevent any possible scroll triggers */
body.jigsaw-playing * {
  scroll-behavior: auto !important;
  -webkit-overflow-scrolling: auto !important;
}

/* Fixed popup lock screen overlay - Prevents all scrolling */
.popup-lock-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto; /* Changed to auto to block all interactions */
  overflow: hidden; /* Prevent any scrolling */
  touch-action: none; /* Prevent touch scrolling */
  -webkit-overflow-scrolling: touch; /* iOS scroll fix */
}

.popup-lock-content {
  pointer-events: auto; /* Re-enables clicking on the popup content */
  background: white;
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 2px solid #f3e8ff;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto; /* Allow scrolling within popup only */
  touch-action: auto; /* Allow touch scrolling within popup */
  -webkit-overflow-scrolling: touch; /* Smooth iOS scrolling */
}

/* Smooth popup animations */
.popup-lock-overlay.entering {
  animation: popupFadeIn 0.3s ease-out;
}

.popup-lock-overlay.exiting {
  animation: popupFadeOut 0.3s ease-in;
}

@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes popupFadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Dark mode removed - light mode only for all platforms */
}

@layer base {
  /* FORCE LIGHT MODE ONLY - COMPREHENSIVE ENFORCEMENT */
  html {
    color-scheme: light only !important;
    background-color: white !important;
  }

  /* Override ALL possible dark mode triggers */
  html.dark,
  html[data-theme="dark"],
  html[data-bs-theme="dark"],
  .dark,
  [data-theme="dark"],
  [data-bs-theme="dark"],
  body.dark,
  div.dark {
    color-scheme: light only !important;
    filter: none !important;
    background-color: white !important;
    color: #1a1a1a !important;
  }

  /* Disable system dark mode preference completely */
  @media (prefers-color-scheme: dark) {
    html, body, *, div, section, main, article, aside, header, footer, nav {
      color-scheme: light only !important;
      background-color: white !important;
      color: #1a1a1a !important;
    }

    /* Force light backgrounds on all gradient elements */
    .bg-gradient-to-br,
    .bg-gradient-to-r,
    .bg-gradient-to-l,
    .bg-gradient-to-t,
    .bg-gradient-to-b {
      background: white !important;
    }
  }

  * {
    @apply border-border;
    /* Force light colors on all elements */
    color-scheme: light only !important;
  }

  body {
    @apply bg-background text-foreground;
    /* Ensure light background always */
    background-color: white !important;
    color: #1a1a1a !important;
    /* Prevent any dark mode styling */
    filter: none !important;
  }

  /* Override any potential dark backgrounds */
  div, section, main, article, aside, header, footer, nav {
    background-color: inherit;
    color: inherit;
  }

  /* Force light mode on all common UI elements */
  button, input, textarea, select {
    background-color: white !important;
    color: #1a1a1a !important;
  }
}

/* ADDITIONAL LIGHT MODE ENFORCEMENT */
/* Override any dark mode classes that might be added dynamically */
.dark\:bg-gray-800,
.dark\:bg-gray-900,
.dark\:bg-black,
.dark\:text-white,
.dark\:text-gray-100,
.dark\:text-gray-200 {
  background-color: white !important;
  color: #1a1a1a !important;
}

/* Force light mode on all possible dark elements */
[class*="dark:"],
[class*="bg-gray-8"],
[class*="bg-gray-9"],
[class*="bg-black"] {
  background-color: white !important;
  color: #1a1a1a !important;
}

/* Prevent any dark backgrounds from system or browser */
* {
  background-color: inherit !important;
}

/* Root level enforcement */
:root {
  color-scheme: light only !important;
  background-color: white !important;
}

/* Browser specific overrides */
html:-webkit-any-link {
  color-scheme: light only !important;
}

html:-moz-any-link {
  color-scheme: light only !important;
}

/* Additional Emoji Support and Fixes */
/* Force emoji rendering for specific elements */
button, .emoji, [class*="emoji"], h1, h2, h3, h4, h5, h6, p, span, div {
  font-variant-emoji: emoji;
  text-rendering: optimizeLegibility;
}

/* Ensure emojis don't break layout */
.emoji, [class*="emoji"] {
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
  font-style: normal;
  font-weight: normal;
  text-decoration: none;
  white-space: nowrap;
}

/* Fix for emoji sizing in different contexts */
.text-xs .emoji, .text-xs [class*="emoji"] { font-size: 0.75rem; }
.text-sm .emoji, .text-sm [class*="emoji"] { font-size: 0.875rem; }
.text-base .emoji, .text-base [class*="emoji"] { font-size: 1rem; }
.text-lg .emoji, .text-lg [class*="emoji"] { font-size: 1.125rem; }
.text-xl .emoji, .text-xl [class*="emoji"] { font-size: 1.25rem; }
.text-2xl .emoji, .text-2xl [class*="emoji"] { font-size: 1.5rem; }
.text-3xl .emoji, .text-3xl [class*="emoji"] { font-size: 1.875rem; }
.text-4xl .emoji, .text-4xl [class*="emoji"] { font-size: 2.25rem; }

/* Fallback for browsers that don't support color emojis */
@supports not (font-variant-emoji: emoji) {
  .emoji::before {
    content: '';
    display: inline-block;
    width: 1em;
    height: 1em;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    vertical-align: middle;
  }
}

/* Additional emoji rendering fixes */
.emoji {
  /* Prevent emoji from being affected by text transforms */
  text-transform: none !important;
  /* Ensure proper spacing */
  letter-spacing: normal;
  /* Prevent font-weight from affecting emojis */
  font-weight: normal !important;
  /* Better emoji alignment */
  vertical-align: baseline;
}

/* Fix for specific emoji rendering issues on Windows */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  .emoji {
    font-family: 'Segoe UI Emoji', 'Segoe UI Symbol', sans-serif !important;
  }
}

/* Fix for Firefox emoji rendering */
@-moz-document url-prefix() {
  .emoji {
    font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', 'EmojiOne Color', sans-serif !important;
  }
}

/* Enhanced Celebration Popup Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(2deg); }
  50% { transform: translateY(-5px) rotate(-1deg); }
  75% { transform: translateY(-15px) rotate(1deg); }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0px);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05) rotate(2deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Apply animations */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-fadeInScale {
  animation: fadeInScale 0.6s ease-out forwards;
}

.animate-slideInUp {
  animation: slideInUp 0.8s ease-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Enhanced button hover effects */
.group:hover .animate-shimmer {
  animation: shimmer 1.5s infinite;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
}

/* Jigsaw puzzle performance optimizations */
.jigsaw-piece {
  /* Hardware acceleration for smooth dragging */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  /* Optimize for frequent position changes */
  will-change: transform, left, top;
  /* Smooth transitions */
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  /* CSS containment for better performance */
  contain: layout style paint;
  /* Optimize rendering */
  isolation: isolate;
}

.jigsaw-piece:active,
.jigsaw-piece[data-dragging="true"] {
  /* Disable transitions during drag for better performance */
  transition: none !important;
  /* Ensure hardware acceleration */
  transform: translateZ(0) scale(1.05);
  /* Optimize rendering during drag */
  will-change: transform, left, top;
}

/* Desktop-specific optimizations - Mobile-like feel */
@media (min-width: 769px) and (hover: hover) {
  .jigsaw-piece {
    /* Mobile-like smooth interactions */
    will-change: transform !important;
    contain: layout style paint !important;
    transform: translateZ(0) !important;
    cursor: grab !important;
    /* Smooth transitions like mobile */
    transition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
    /* Enable subpixel rendering for smoother movement */
    transform-style: preserve-3d !important;
    backface-visibility: hidden !important;
  }

  .jigsaw-piece:hover {
    /* Mobile-like hover feedback */
    transform: translateZ(0) scale(1.01) !important;
    filter: brightness(1.02) !important;
    transition: all 0.15s ease-out !important;
  }

  .jigsaw-piece:active {
    /* Mobile-like press feedback */
    transform: translateZ(0) scale(1.02) !important;
    filter: brightness(1.05) drop-shadow(0 4px 12px rgba(0,0,0,0.2)) !important;
    cursor: grabbing !important;
    transition: all 0.1s ease-out !important;
  }

  .jigsaw-piece[data-dragging="true"] {
    /* Mobile-like dragging state */
    cursor: grabbing !important;
    filter: brightness(1.1) drop-shadow(0 8px 16px rgba(0,0,0,0.3)) !important;
    z-index: 9999 !important;
    /* Smooth movement during drag */
    transition: filter 0.1s ease-out !important;
  }

  /* Optimize container for desktop */
  .puzzle-container {
    /* Enable hardware acceleration */
    transform: translateZ(0) !important;
    /* Mobile-like smooth interactions */
    -webkit-overflow-scrolling: touch !important;
    /* Better containment */
    contain: layout style !important;
  }

  /* Mobile-like button interactions */
  button {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  button:hover {
    transform: translateY(-1px) scale(1.02) !important;
    filter: brightness(1.05) !important;
  }

  button:active {
    transform: translateY(0) scale(0.98) !important;
    filter: brightness(0.95) !important;
  }
}

/* Mobile-like ripple effect for desktop */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.jigsaw-piece::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
  pointer-events: none;
  z-index: 1;
}

.jigsaw-piece:active::before {
  width: 100px;
  height: 100px;
  animation: ripple 0.6s ease-out;
}

/* Smooth mobile-like piece movement */
@media (min-width: 769px) {
  .jigsaw-piece {
    /* Add subtle mobile-like bounce */
    animation: none;
  }

  .jigsaw-piece:hover {
    animation: subtle-bounce 0.6s ease-in-out;
  }
}

@keyframes subtle-bounce {
  0%, 100% { transform: translateZ(0) scale(1); }
  50% { transform: translateZ(0) scale(1.01); }
}

.jigsaw-piece:active,
.jigsaw-piece[data-dragging="true"] {
  /* Disable transitions during drag for better performance */
  transition: none !important;
  /* Ensure hardware acceleration */
  transform: translateZ(0) scale(1.05);
  /* Optimize rendering during drag */
  will-change: transform, left, top;
}

/* Puzzle container optimizations */
.puzzle-container {
  /* Enable hardware acceleration */
  transform: translateZ(0);
  /* Optimize for frequent updates */
  will-change: contents;
  /* Improve touch responsiveness */
  touch-action: none;
  /* Prevent text selection during drag */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Mobile-specific performance optimizations */
@media (max-width: 768px) {
  /* Reduce GPU memory usage on mobile */
  .jigsaw-piece {
    will-change: transform !important; /* Only transform, not left/top */
    contain: strict !important; /* Aggressive containment */
    transform-style: flat !important; /* Disable 3D transforms */
    perspective: none !important;
    backface-visibility: visible !important; /* Reduce GPU usage */
  }

  /* Optimize container for mobile */
  .puzzle-container {
    will-change: auto !important; /* Reduce GPU usage */
    contain: layout style !important;
    transform: none !important; /* Disable unnecessary transforms */
  }

  /* Reduce animation complexity */
  .animate-bounce,
  .animate-pulse,
  .animate-spin {
    animation-duration: 1s !important; /* Faster animations */
    animation-timing-function: ease-out !important;
  }

  /* Optimize celebration popup for mobile */
  .fixed {
    contain: layout style paint !important;
  }

  /* Reduce backdrop blur on mobile for better performance */
  .backdrop-blur-sm,
  .backdrop-blur-md {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    background-color: rgba(0, 0, 0, 0.8) !important;
  }

  /* Optimize gradients on mobile */
  .bg-gradient-to-br,
  .bg-gradient-to-r {
    background-image: none !important;
    background-color: #f8fafc !important;
  }

  /* Reduce shadow complexity */
  .shadow-2xl,
  .shadow-xl,
  .shadow-lg {
    box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
  }

  /* Optimize text rendering */
  * {
    text-rendering: optimizeSpeed !important;
    -webkit-font-smoothing: subpixel-antialiased !important;
  }
}