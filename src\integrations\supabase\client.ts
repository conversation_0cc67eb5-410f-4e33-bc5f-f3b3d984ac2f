// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mxdkmrfzfsvjwgqzikpz.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im14ZGttcmZ6ZnN2andncXppa3B6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwNTIyODQsImV4cCI6MjA2NTYyODI4NH0.4_4IFhu7X5VKXRLmgQF1suLuQGVsO-yob3-b8bXAyM0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);