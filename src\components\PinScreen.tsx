
import React, { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';

interface PinScreenProps {
  onUnlock: () => void;
}

const PinScreen = ({ onUnlock }: PinScreenProps) => {
  const [pin, setPin] = useState('');
  const [attempts, setAttempts] = useState(0);
  const [shake, setShake] = useState(false);
  const correctPins = ['180724', '180767']; // Multiple valid PINs
  
  const handleNumberClick = (num: string) => {
    if (pin.length < 6) {
      setPin(prev => prev + num);
    }
  };

  const handleClear = () => {
    setPin('');
  };

  const handleSubmit = () => {
    if (correctPins.includes(pin)) {
      onUnlock();
    } else {
      setAttempts(prev => prev + 1);
      setShake(true);
      setPin('');
      setTimeout(() => setShake(false), 500);
    }
  };

  useEffect(() => {
    if (pin.length === 6) {
      handleSubmit();
    }
  }, [pin]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-100 via-purple-50 to-indigo-100 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Floating Hearts Background */}
      {[...Array(20)].map((_, i) => (
        <Heart
          key={i}
          className={`absolute text-pink-300 opacity-30 animate-pulse`}
          size={Math.random() * 25 + 15}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 4}s`,
            animationDuration: `${2 + Math.random() * 3}s`
          }}
        />
      ))}

      {/* Sparkle Effects */}
      {[...Array(10)].map((_, i) => (
        <div
          key={`sparkle-${i}`}
          className="absolute text-yellow-300 opacity-40 animate-ping emoji"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 3}s`,
            animationDuration: `${1.5 + Math.random() * 2}s`
          }}
        >
          ✨
        </div>
      ))}

      <div className="bg-white/90 backdrop-blur-md rounded-3xl p-6 md:p-8 shadow-2xl max-w-xs sm:max-w-sm w-full mx-4 border border-pink-100">
        <div className="text-center mb-6 md:mb-8">
          <div className="mb-4">
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent mb-3 font-thai leading-tight tracking-wide">
              <span className="emoji">💕</span> ครบรอบ 1 ปี <span className="emoji">💕</span>
            </h1>
            <p className="text-base sm:text-lg md:text-xl font-medium text-gray-700 mb-2 font-thai leading-relaxed">
              เกมจิ๊กซอว์ความทรงจำ <span className="emoji">🧩</span>
            </p>
            <p className="text-sm sm:text-base md:text-lg font-medium text-pink-600 mb-2 font-thai leading-relaxed">
              ข้อความจากใจ <span className="emoji">💕</span>
            </p>
            <p className="text-sm sm:text-base md:text-lg font-medium text-purple-600 mb-3 font-thai leading-relaxed">
              รักแมวจ๋องมากๆ นะ <span className="emoji">💕</span>
            </p>
            <div className="flex justify-center space-x-1 mb-2">
              {['🌸', '💖', '🌺', '💕', '🌸'].map((emoji, i) => (
                <span
                  key={i}
                  className="text-lg emoji animate-bounce"
                  style={{animationDelay: `${i * 0.1}s`}}
                >
                  {emoji}
                </span>
              ))}
            </div>
          </div>
          <p className="text-gray-600 text-sm sm:text-base font-thai leading-relaxed">ใส่วันที่เราเป็นของกันและกัน</p>
          {attempts > 0 && (
            <div className="mt-3 p-3 bg-pink-50 rounded-xl border border-pink-200">
              <p className="text-pink-600 text-sm font-thai leading-relaxed">
                {attempts >= 3 ? (
                  <>แมวจ๋องลืมอ่อ !!! <span className="emoji">😘💭</span></>
                ) : (
                  <>ลองอีกครั้งนะคะ... เหลือ {3 - attempts} ครั้ง <span className="emoji">🤔</span></>
                )}
              </p>
            </div>
          )}
        </div>

        {/* PIN Display */}
        <div className={`flex justify-center mb-6 md:mb-8 ${shake ? 'animate-bounce' : ''}`}>
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className={`w-3 h-3 sm:w-4 sm:h-4 mx-1 sm:mx-2 rounded-full border-2 transition-all duration-300 ${
                i < pin.length
                  ? 'bg-gradient-to-r from-pink-400 to-purple-400 border-pink-400 scale-110 shadow-lg'
                  : 'bg-transparent border-gray-300 hover:border-pink-200'
              }`}
            >
              {i < pin.length && (
                <div className="w-full h-full rounded-full bg-white/30 animate-pulse"></div>
              )}
            </div>
          ))}
        </div>

        {/* Number Pad */}
        <div className="grid grid-cols-3 gap-3 sm:gap-4 mb-4 md:mb-6">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
            <button
              key={num}
              onClick={() => handleNumberClick(num.toString())}
              className="aspect-square rounded-2xl bg-gradient-to-br from-pink-50 to-purple-50 hover:from-pink-100 hover:to-purple-100 transition-all duration-200 flex items-center justify-center text-lg sm:text-xl font-semibold text-gray-700 shadow-lg hover:shadow-xl active:scale-95 border border-pink-100 hover:border-pink-200"
            >
              {num}
            </button>
          ))}
          <button
            onClick={handleClear}
            className="aspect-square rounded-2xl bg-gradient-to-br from-red-50 to-pink-50 hover:from-red-100 hover:to-pink-100 transition-all duration-200 flex items-center justify-center text-xs sm:text-sm font-medium text-red-600 shadow-lg hover:shadow-xl active:scale-95 border border-red-100 hover:border-red-200 font-thai"
          >
            ลบ
          </button>
          <button
            onClick={() => handleNumberClick('0')}
            className="aspect-square rounded-2xl bg-gradient-to-br from-pink-50 to-purple-50 hover:from-pink-100 hover:to-purple-100 transition-all duration-200 flex items-center justify-center text-lg sm:text-xl font-semibold text-gray-700 shadow-lg hover:shadow-xl active:scale-95 border border-pink-100 hover:border-pink-200"
          >
            0
          </button>
          <div></div> {/* Empty space */}
          <button
            onClick={handleSubmit}
            className="aspect-square rounded-2xl bg-gradient-to-br from-green-50 to-emerald-50 hover:from-green-100 hover:to-emerald-100 transition-all duration-200 flex items-center justify-center text-xs sm:text-sm font-medium text-green-600 shadow-lg hover:shadow-xl active:scale-95 border border-green-100 hover:border-green-200 font-thai"
          >
            ✓
          </button>
        </div>

        <p className="text-center text-sm text-gray-500">
          คำใบ้: วันที่เราคบกัน! (DDMMYY)
        </p>
      </div>
    </div>
  );
};

export default PinScreen;
