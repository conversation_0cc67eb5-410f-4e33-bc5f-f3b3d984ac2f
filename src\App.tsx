import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

// AGGRESSIVE LIGHT MODE ENFORCER
const useForceLightMode = () => {
  useEffect(() => {
    const enforceLightMode = () => {
      // Force light mode on document root
      document.documentElement.style.colorScheme = 'light only';
      document.documentElement.style.backgroundColor = 'white';
      document.documentElement.classList.remove('dark');
      document.documentElement.removeAttribute('data-theme');

      // Force light mode on body
      document.body.style.backgroundColor = 'white';
      document.body.style.color = '#1a1a1a';
      document.body.classList.remove('dark');

      // Remove all dark mode classes
      const darkElements = document.querySelectorAll('.dark, [data-theme="dark"]');
      darkElements.forEach(el => {
        el.classList.remove('dark');
        el.removeAttribute('data-theme');
        (el as HTMLElement).style.backgroundColor = 'white';
        (el as HTMLElement).style.color = '#1a1a1a';
      });
    };

    // Enforce immediately
    enforceLightMode();

    // Continuous enforcement
    const interval = setInterval(enforceLightMode, 200);

    // Enforce on DOM changes
    const observer = new MutationObserver(enforceLightMode);
    observer.observe(document.documentElement, {
      attributes: true,
      subtree: true,
      childList: true
    });

    // Enforce on system changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = () => enforceLightMode();

    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      mediaQuery.addListener(handleChange);
    }

    return () => {
      clearInterval(interval);
      observer.disconnect();
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange);
      } else {
        mediaQuery.removeListener(handleChange);
      }
    };
  }, []);
};

const App = () => {
  // Force light mode
  useForceLightMode();

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
