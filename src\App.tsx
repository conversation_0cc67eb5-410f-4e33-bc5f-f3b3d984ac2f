import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useEffect } from "react";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

// ENHANCED LIGHT MODE ENFORCER FOR MOBILE PORTRAIT
const useForceLightMode = () => {
  useEffect(() => {
    const enforceLightMode = () => {
      // Force light mode on document
      document.documentElement.style.colorScheme = 'light only';
      document.documentElement.style.backgroundColor = 'white';
      document.documentElement.style.color = '#1a1a1a';
      document.documentElement.classList.remove('dark');
      document.documentElement.removeAttribute('data-theme');

      // Force light mode on body
      document.body.style.backgroundColor = 'white';
      document.body.style.color = '#1a1a1a';
      document.body.classList.remove('dark');

      // Force light mode on all elements to prevent dark backgrounds
      const allElements = document.querySelectorAll('*');
      allElements.forEach(el => {
        const element = el as HTMLElement;
        // Remove any dark classes
        element.classList.remove('dark');
        element.removeAttribute('data-theme');

        // Force light backgrounds on specific problematic elements
        if (element.style.backgroundColor === 'black' ||
            element.style.backgroundColor === 'rgb(0, 0, 0)' ||
            element.classList.contains('bg-black') ||
            element.classList.contains('bg-gray-900') ||
            element.classList.contains('bg-gray-800')) {
          element.style.backgroundColor = 'white';
          element.style.color = '#1a1a1a';
        }
      });
    };

    // Enforce immediately
    enforceLightMode();

    // Continuous enforcement
    const interval = setInterval(enforceLightMode, 500);

    // Enforce on orientation changes
    const handleOrientationChange = () => {
      setTimeout(enforceLightMode, 100);
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);

    return () => {
      clearInterval(interval);
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', handleOrientationChange);
    };
  }, []);
};

const App = () => {
  // Force light mode
  useForceLightMode();

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
