import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useEffect } from "react";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

// NUCLEAR LIGHT MODE ENFORCER - NO DARK ALLOWED
const useForceLightMode = () => {
  useEffect(() => {
    const enforceLightMode = () => {
      // Force light mode on document
      document.documentElement.style.colorScheme = 'light only';
      document.documentElement.style.backgroundColor = 'white';
      document.documentElement.style.background = 'white';
      document.documentElement.style.color = '#1a1a1a';
      document.documentElement.classList.remove('dark');

      // Force light mode on body
      document.body.style.backgroundColor = 'white';
      document.body.style.background = 'white';
      document.body.style.color = '#1a1a1a';
      document.body.classList.remove('dark');

      // NUCLEAR OPTION: Force white on EVERY element
      const allElements = document.querySelectorAll('*');
      allElements.forEach(el => {
        const element = el as HTMLElement;

        // Force white background on ALL elements
        element.style.backgroundColor = 'white';
        element.style.background = 'white';
        element.style.color = '#1a1a1a';
        element.classList.remove('dark');
        element.removeAttribute('data-theme');

        // Remove ALL dark-related classes
        const classList = Array.from(element.classList);
        classList.forEach(className => {
          if (className.includes('dark') ||
              className.includes('bg-black') ||
              className.includes('bg-gray-8') ||
              className.includes('bg-gray-9')) {
            element.classList.remove(className);
          }
        });
      });
    };

    // Enforce immediately
    enforceLightMode();

    // VERY frequent enforcement
    const interval = setInterval(enforceLightMode, 100);

    // Enforce on ANY change
    const handleChange = () => {
      enforceLightMode();
      setTimeout(enforceLightMode, 50);
      setTimeout(enforceLightMode, 200);
    };

    window.addEventListener('orientationchange', handleChange);
    window.addEventListener('resize', handleChange);
    window.addEventListener('focus', handleChange);
    window.addEventListener('blur', handleChange);

    return () => {
      clearInterval(interval);
      window.removeEventListener('orientationchange', handleChange);
      window.removeEventListener('resize', handleChange);
      window.removeEventListener('focus', handleChange);
      window.removeEventListener('blur', handleChange);
    };
  }, []);
};

const App = () => {
  // Force light mode
  useForceLightMode();

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
