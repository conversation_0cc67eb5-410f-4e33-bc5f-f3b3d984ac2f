import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useEffect } from "react";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

// SIMPLE LIGHT MODE ENFORCER
const useForceLightMode = () => {
  useEffect(() => {
    const enforceLightMode = () => {
      // Force light mode on document and body only
      document.documentElement.style.colorScheme = 'light only';
      document.documentElement.style.backgroundColor = 'white';
      document.body.style.backgroundColor = 'white';
      document.body.style.color = '#1a1a1a';
    };

    // Enforce on orientation change only
    const handleOrientationChange = () => {
      setTimeout(enforceLightMode, 100);
    };

    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);
};

const App = () => {
  // Force light mode
  useForceLightMode();

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
