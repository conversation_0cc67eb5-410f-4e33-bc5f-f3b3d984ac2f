
<!DOCTYPE html>
<html lang="th">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="color-scheme" content="light only" />
    <title>ครบรอบ 1 ปี 💕</title>
    <meta name="description" content="เว็บไซต์ครบรอบ 1 ปีที่เต็มไปด้วยความรักและความทรงจำดีๆ" />
    <meta name="author" content="Lovable" />
    
    <!-- Google Fonts - Thai Font & Emoji Support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@300;400;500;600;700&family=Sarabun:wght@300;400;500;600;700&family=Noto+Color+Emoji&display=swap" rel="stylesheet">

    <!-- SIMPLE LIGHT MODE ENFORCEMENT -->
    <script>
      (function() {
        // Simple light mode enforcement
        const enforceLightMode = function() {
          document.documentElement.style.colorScheme = 'light only';
          document.documentElement.style.backgroundColor = 'white';

          if (document.body) {
            document.body.style.backgroundColor = 'white';
            document.body.style.color = '#1a1a1a';
          }
        };

        // Force light mode immediately
        enforceLightMode();

        // Force light mode on orientation change
        window.addEventListener('orientationchange', function() {
          setTimeout(enforceLightMode, 100);
        });

        // Force light mode on page load
        document.addEventListener('DOMContentLoaded', enforceLightMode);
      })();
    </script>

  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
