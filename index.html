
<!DOCTYPE html>
<html lang="th">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="color-scheme" content="light only" />
    <title>ครบรอบ 1 ปี 💕</title>
    <meta name="description" content="เว็บไซต์ครบรอบ 1 ปีที่เต็มไปด้วยความรักและความทรงจำดีๆ" />
    <meta name="author" content="Lovable" />

    <!-- FORCE LIGHT MODE IMMEDIATELY -->
    <script>
      (function() {
        // Force light mode before any content loads
        document.documentElement.style.colorScheme = 'light only';
        document.documentElement.style.backgroundColor = 'white';
        document.documentElement.classList.remove('dark');
        document.documentElement.removeAttribute('data-theme');

        // Prevent any dark mode from being applied
        const observer = new MutationObserver(function() {
          document.documentElement.style.colorScheme = 'light only';
          document.documentElement.style.backgroundColor = 'white';
          document.documentElement.classList.remove('dark');
          document.body.style.backgroundColor = 'white';
          document.body.style.color = '#1a1a1a';
        });

        observer.observe(document.documentElement, {
          attributes: true,
          subtree: true
        });

        // Force light mode on page load
        document.addEventListener('DOMContentLoaded', function() {
          document.body.style.backgroundColor = 'white';
          document.body.style.color = '#1a1a1a';
        });
      })();
    </script>
    
    <!-- Google Fonts - Thai Font & Emoji Support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@300;400;500;600;700&family=Sarabun:wght@300;400;500;600;700&family=Noto+Color+Emoji&display=swap" rel="stylesheet">

  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
