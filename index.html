
<!DOCTYPE html>
<html lang="th">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="color-scheme" content="light only" />
    <title>ครบรอบ 1 ปี 💕</title>
    <meta name="description" content="เว็บไซต์ครบรอบ 1 ปีที่เต็มไปด้วยความรักและความทรงจำดีๆ" />
    <meta name="author" content="Lovable" />
    
    <!-- Google Fonts - Thai Font & Emoji Support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@300;400;500;600;700&family=Sarabun:wght@300;400;500;600;700&family=Noto+Color+Emoji&display=swap" rel="stylesheet">

    <!-- AGGRESSIVE LIGHT MODE ENFORCEMENT FOR MOBILE PORTRAIT -->
    <script>
      (function() {
        // Enhanced enforcement function
        const enforceLightMode = function() {
          document.documentElement.style.colorScheme = 'light only';
          document.documentElement.style.backgroundColor = 'white';
          document.documentElement.style.color = '#1a1a1a';
          document.documentElement.classList.remove('dark');

          if (document.body) {
            document.body.style.backgroundColor = 'white';
            document.body.style.color = '#1a1a1a';
            document.body.classList.remove('dark');
          }

          // Force light mode on all elements to prevent dark backgrounds
          const allElements = document.querySelectorAll('*');
          allElements.forEach(function(el) {
            if (el.style.backgroundColor === 'black' ||
                el.style.backgroundColor === 'rgb(0, 0, 0)' ||
                el.classList.contains('bg-black') ||
                el.classList.contains('dark')) {
              el.style.backgroundColor = 'white';
              el.style.color = '#1a1a1a';
              el.classList.remove('dark');
            }
          });
        };

        // Force light mode immediately
        enforceLightMode();

        // Continuous enforcement every 100ms
        setInterval(enforceLightMode, 100);

        // Monitor DOM changes
        const observer = new MutationObserver(enforceLightMode);
        observer.observe(document.documentElement, {
          attributes: true,
          subtree: true,
          childList: true
        });

        // Force light mode on orientation change (critical for mobile portrait)
        window.addEventListener('orientationchange', function() {
          setTimeout(enforceLightMode, 50);
          setTimeout(enforceLightMode, 200);
          setTimeout(enforceLightMode, 500);
        });

        // Force light mode on resize
        window.addEventListener('resize', function() {
          setTimeout(enforceLightMode, 50);
        });

        // Force light mode on page load
        document.addEventListener('DOMContentLoaded', enforceLightMode);
      })();
    </script>

  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
