
<!DOCTYPE html>
<html lang="th">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="color-scheme" content="light only" />
    <title>ครบรอบ 1 ปี 💕</title>
    <meta name="description" content="เว็บไซต์ครบรอบ 1 ปีที่เต็มไปด้วยความรักและความทรงจำดีๆ" />
    <meta name="author" content="Lovable" />
    
    <!-- Google Fonts - Thai Font & Emoji Support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@300;400;500;600;700&family=Sarabun:wght@300;400;500;600;700&family=Noto+Color+Emoji&display=swap" rel="stylesheet">

    <!-- ULTRA AGGRESSIVE LIGHT MODE - NO DARK ALLOWED -->
    <script>
      (function() {
        // NUCLEAR OPTION - Force white on everything
        const enforceLightMode = function() {
          // Force light mode on document
          document.documentElement.style.colorScheme = 'light only';
          document.documentElement.style.backgroundColor = 'white';
          document.documentElement.style.background = 'white';
          document.documentElement.style.color = '#1a1a1a';
          document.documentElement.classList.remove('dark');

          // Force light mode on body
          if (document.body) {
            document.body.style.backgroundColor = 'white';
            document.body.style.background = 'white';
            document.body.style.color = '#1a1a1a';
            document.body.classList.remove('dark');
          }

          // FORCE WHITE ON EVERY SINGLE ELEMENT
          const allElements = document.querySelectorAll('*');
          allElements.forEach(function(el) {
            // Force white background on ALL elements
            el.style.backgroundColor = 'white';
            el.style.background = 'white';
            el.style.color = '#1a1a1a';
            el.classList.remove('dark');
            el.removeAttribute('data-theme');

            // Remove any dark classes
            const classList = Array.from(el.classList);
            classList.forEach(function(className) {
              if (className.includes('dark') ||
                  className.includes('bg-black') ||
                  className.includes('bg-gray-8') ||
                  className.includes('bg-gray-9')) {
                el.classList.remove(className);
              }
            });
          });
        };

        // Force light mode immediately
        enforceLightMode();

        // Reasonable enforcement every 1000ms
        setInterval(enforceLightMode, 1000);

        // Monitor DOM changes
        const observer = new MutationObserver(enforceLightMode);
        observer.observe(document.documentElement, {
          attributes: true,
          subtree: true,
          childList: true
        });

        // Force light mode on orientation change (critical for mobile portrait)
        window.addEventListener('orientationchange', function() {
          setTimeout(enforceLightMode, 50);
          setTimeout(enforceLightMode, 200);
          setTimeout(enforceLightMode, 500);
        });

        // Force light mode on resize
        window.addEventListener('resize', function() {
          setTimeout(enforceLightMode, 50);
        });

        // Force light mode on page load
        document.addEventListener('DOMContentLoaded', enforceLightMode);
      })();
    </script>

  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
